from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class Ticket:
    ticket_id: str
    user_id: int
    channel_id: int
    category: str
    status: str
    title: str
    created_at: datetime
    closed_at: Optional[datetime] = None
    solved_at: Optional[datetime] = None
    closed_by: Optional[int] = None
    solved_by: Optional[int] = None

@dataclass
class TicketMessage:
    ticket_id: str
    user_id: int
    message: str
    is_admin: bool
    created_at: datetime

@dataclass
class AdminStats:
    admin_id: int
    tickets_closed: int
    tickets_solved: int
    avg_response_time: float
    total_responses: int
    last_updated: datetime