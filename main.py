import discord
from discord import app_commands
from discord.ext import commands
from config import Config
from database import db
from utils import TicketView, TicketActionsView, setup_ticket_system
import asyncio
import re

class TicketBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.messages = True
        intents.guilds = True
        intents.members = True
        intents.message_content = True
        
        # إزالة command_sync_flags
        super().__init__(command_prefix="!", intents=intents)
        self.db = db
        self.ticket_counter = 0

    async def setup_hook(self):
        await self.db.create_pool()
        await setup_ticket_system(self)
        await self.load_ticket_counter()
        
        # مزامنة الأوامر مع الخوادم
        try:
            synced = await self.tree.sync()
            print(f"تم مزامنة {len(synced)} أمر")
        except Exception as e:
            print(f"فشل في مزامنة الأوامر: {e}")

    async def on_ready(self):
        print(f'تم تسجيل الدخول كـ {self.user}')
        await self.change_presence(activity=discord.Activity(type=discord.ActivityType.watching, name="التذاكر المفتوحة"))

    async def load_ticket_counter(self):
        async with self.db.pool.acquire() as conn:
            async with conn.cursor() as cur:
                await cur.execute("SELECT COUNT(*) FROM tickets")
                result = await cur.fetchone()
                self.ticket_counter = result[0] if result else 0

    async def get_ticket_number(self):
        self.ticket_counter += 1
        return self.ticket_counter

bot = TicketBot()

@bot.tree.command(name="setup", description="إعداد نظام التذاكر")
@app_commands.default_permissions(administrator=True)
async def setup_tickets(interaction: discord.Interaction):
    embed = discord.Embed(
        title="نظام الدعم الفني",
        description="اختر قسم الدعم الفني المناسب لفتح تذكرة جديدة",
        color=Config.COLORS['info']
    )
    
    view = TicketView(bot)
    await interaction.channel.send(embed=embed, view=view)
    await interaction.response.send_message("تم إعداد نظام التذاكر بنجاح!", ephemeral=True)

@bot.tree.command(name="done", description="وضع التذكرة كمحلولة مع وقت معين")
@app_commands.describe(time="وقت الإغلاق (مثال: 12h)")
async def mark_done(interaction: discord.Interaction, time: str = "12h"):
    # التحقق من أن القناة هي قناة تذكرة
    if not interaction.channel.name.startswith(('⚪', '🟢', '✅', '🔒')):
        embed = discord.Embed(
            title="خطأ",
            description="هذا الأمر يمكن استخدامه فقط في قنوات التذاكر",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # الحصول على رقم التذكرة من اسم القناة
    ticket_id = interaction.channel.name.split('-', 1)[1] if '-' in interaction.channel.name else None
    
    if not ticket_id:
        embed = discord.Embed(
            title="خطأ",
            description="تعذر التعرف على رقم التذكرة",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # التحقق من صلاحية المستخدم
    is_admin = any(role.id in Config.ROLE_IDS.values() for role in interaction.user.roles)
    if not is_admin:
        embed = discord.Embed(
            title="خطأ في الصلاحية",
            description="لا تملك الصلاحية اللازمة لتنفيذ هذا الأمر.",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # تحويل الوقت إلى ثواني
    time_seconds = 43200  # 12 ساعة افتراضياً
    if time.endswith('h'):
        try:
            time_seconds = int(time[:-1]) * 3600
        except ValueError:
            pass
    elif time.endswith('m'):
        try:
            time_seconds = int(time[:-1]) * 60
        except ValueError:
            pass
    
    # تحديث حالة التذكرة
    async with bot.db.pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(
                "UPDATE tickets SET status = 'solved', solved_at = NOW(), solved_by = %s WHERE ticket_id = %s",
                (interaction.user.id, ticket_id)
            )
            
            # تحديث إحصائيات الأدمن
            await cur.execute(
                "INSERT INTO admin_stats (admin_id, tickets_solved) VALUES (%s, 1) ON DUPLICATE KEY UPDATE tickets_solved = tickets_solved + 1",
                (interaction.user.id,)
            )
    
    # تغيير اسم الروم
    await interaction.channel.edit(name=interaction.channel.name.replace("⚪", "✅").replace("🟢", "✅"))
    
    # إرسال رسالة الحل
    embed = discord.Embed(
        title="تم حل التذكرة",
        description=f"تم حل هذه التذكرة بواسطة {interaction.user.mention} وسيتم إغلاقها تلقائياً بعد {time}",
        color=Config.COLORS['solved']
    )
    await interaction.response.send_message(embed=embed)
    
    # إغلاق التذكرة بعد الوقت المحدد
    await asyncio.sleep(time_seconds)
    
    # إغلاق التذكرة نهائياً
    closed_category_id = Config.CATEGORY_IDS.get('closed_tickets')
    if closed_category_id:
        closed_category = interaction.guild.get_channel(closed_category_id)
    else:
        closed_category = await interaction.guild.create_category("التذاكر المغلقة")
    
    await interaction.channel.edit(category=closed_category, name=interaction.channel.name.replace("✅", "🔒"))
    
    async with bot.db.pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(
                "UPDATE tickets SET status = 'closed', closed_at = NOW() WHERE ticket_id = %s",
                (ticket_id,)
            )
    
    # إزالة صلاحيات الكتابة من الجميع
    for member in interaction.channel.members:
        if member != bot.user and not any(role.id in Config.ROLE_IDS.values() for role in member.roles):
            await interaction.channel.set_permissions(member, send_messages=False, read_messages=True)

@bot.tree.command(name="rename", description="تغيير اسم التذكرة")
@app_commands.describe(new_name="الاسم الجديد للتذكرة")
async def rename_ticket(interaction: discord.Interaction, new_name: str):
    # التحقق من أن القناة هي قناة تذكرة
    if not interaction.channel.name.startswith(('⚪', '🟢', '✅', '🔒')):
        embed = discord.Embed(
            title="خطأ",
            description="هذا الأمر يمكن استخدامه فقط في قنوات التذاكر",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # التحقق من صلاحية المستخدم
    is_admin = any(role.id in Config.ROLE_IDS.values() for role in interaction.user.roles)
    if not is_admin:
        embed = discord.Embed(
            title="خطأ في الصلاحية",
            description="لا تملك الصلاحية اللازمة لتنفيذ هذا الأمر.",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # الحصول على رقم التذكرة من اسم القناة
    ticket_id = interaction.channel.name.split('-', 1)[1] if '-' in interaction.channel.name else None
    
    if not ticket_id:
        embed = discord.Embed(
            title="خطأ",
            description="تعذر التعرف على رقم التذكرة",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # الحصول على الحالة الحالية للتذكرة
    async with bot.db.pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(
                "SELECT status FROM tickets WHERE ticket_id = %s",
                (ticket_id,)
            )
            result = await cur.fetchone()
            
            if not result:
                embed = discord.Embed(
                    title="خطأ",
                    description="لم يتم العثور على التذكرة في قاعدة البيانات",
                    color=Config.COLORS['closed']
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            status = result[0]
            status_emoji = {
                'open': '⚪',
                'pending': '🟢',
                'solved': '✅',
                'closed': '🔒'
            }.get(status, '⚪')
    
    # تغيير اسم القناة
    new_channel_name = f"{status_emoji}-{new_name}"
    await interaction.channel.edit(name=new_channel_name)
    
    # تحديث اسم التذكرة في قاعدة البيانات
    async with bot.db.pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(
                "UPDATE tickets SET title = %s WHERE ticket_id = %s",
                (new_name, ticket_id)
            )
    
    embed = discord.Embed(
        title="تم تغيير اسم التذكرة",
        description=f"تم تغيير اسم التذكرة إلى: {new_name}",
        color=Config.COLORS['info']
    )
    await interaction.response.send_message(embed=embed)

@bot.tree.command(name="add", description="إضافة عضو إلى التذكرة")
@app_commands.describe(member="العضو الذي تريد إضافته")
async def add_member(interaction: discord.Interaction, member: discord.Member):
    # التحقق من أن القناة هي قناة تذكرة
    if not interaction.channel.name.startswith(('⚪', '🟢', '✅', '🔒')):
        embed = discord.Embed(
            title="خطأ",
            description="هذا الأمر يمكن استخدامه فقط في قنوات التذاكر",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # التحقق من صلاحية المستخدم
    is_admin = any(role.id in Config.ROLE_IDS.values() for role in interaction.user.roles)
    if not is_admin:
        embed = discord.Embed(
            title="خطأ في الصلاحية",
            description="لا تملك الصلاحية اللازمة لتنفيذ هذا الأمر.",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # الحصول على رقم التذكرة من اسم القناة
    ticket_id = interaction.channel.name.split('-', 1)[1] if '-' in interaction.channel.name else None
    
    if not ticket_id:
        embed = discord.Embed(
            title="خطأ",
            description="تعذر التعرف على رقم التذكرة",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # إضافة العضو إلى التذكرة
    await interaction.channel.set_permissions(member, view_channel=True, send_messages=True, read_message_history=True)
    
    # حفظ العضو في قاعدة البيانات
    async with bot.db.pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(
                "INSERT INTO ticket_members (ticket_id, user_id) VALUES (%s, %s)",
                (ticket_id, member.id)
            )
    
    embed = discord.Embed(
        title="تم إضافة العضو",
        description=f"تم إضافة {member.mention} إلى التذكرة بنجاح",
        color=Config.COLORS['info']
    )
    await interaction.response.send_message(embed=embed)

@bot.tree.command(name="stats", description="عرض إحصائيات الدعم الفني")
async def show_stats(interaction: discord.Interaction):
    # التحقق من صلاحية المستخدم
    is_admin = any(role.id in Config.ROLE_IDS.values() for role in interaction.user.roles)
    if not is_admin:
        embed = discord.Embed(
            title="خطأ في الصلاحية",
            description="لا تملك الصلاحية اللازمة لتنفيذ هذا الأمر.",
            color=Config.COLORS['closed']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
    
    # جلب الإحصائيات من قاعدة البيانات
    async with bot.db.pool.acquire() as conn:
        async with conn.cursor() as cur:
            # إحصائيات عامة
            await cur.execute("SELECT COUNT(*) FROM tickets")
            total_tickets = (await cur.fetchone())[0]
            
            await cur.execute("SELECT COUNT(*) FROM tickets WHERE status = 'open'")
            open_tickets = (await cur.fetchone())[0]
            
            await cur.execute("SELECT COUNT(*) FROM tickets WHERE status = 'closed'")
            closed_tickets = (await cur.fetchone())[0]
            
            await cur.execute("SELECT COUNT(*) FROM tickets WHERE status = 'solved'")
            solved_tickets = (await cur.fetchone())[0]
            
            # أفضل الأدمن
            await cur.execute("""
                SELECT admin_id, tickets_closed + tickets_solved as total, tickets_closed, tickets_solved 
                FROM admin_stats 
                ORDER BY total DESC 
                LIMIT 5
            """)
            top_admins = await cur.fetchall()
    
    # إنشاء Embed للإحصائيات
    embed = discord.Embed(
        title="إحصائيات الدعم الفني",
        color=Config.COLORS['info']
    )
    
    embed.add_field(name="إجمالي التذاكر", value=str(total_tickets), inline=True)
    embed.add_field(name="التذاكر المفتوحة", value=str(open_tickets), inline=True)
    embed.add_field(name="التذاكر المغلقة", value=str(closed_tickets), inline=True)
    embed.add_field(name="التذاكر المحلولة", value=str(solved_tickets), inline=True)
    
    if top_admins:
        admins_text = ""
        for i, (admin_id, total, closed, solved) in enumerate(top_admins, 1):
            admin = bot.get_user(admin_id)
            admin_name = admin.name if admin else f"مستخدم {admin_id}"
            admins_text += f"{i}. {admin_name} - إجمالي: {total} (مغلقة: {closed}, محلولة: {solved})\n"
        
        embed.add_field(name="أفضل الأدمن", value=admins_text, inline=False)
    
    await interaction.response.send_message(embed=embed)

@bot.event
async def on_interaction(interaction: discord.Interaction):
    try:
        if hasattr(interaction, 'data') and 'custom_id' in interaction.data:
            custom_id = interaction.data['custom_id']
            
            # التعامل مع أزرار إغلاق التذكرة
            if custom_id.startswith('close_'):
                ticket_id = custom_id.replace('close_', '')
                await handle_close_ticket(interaction, ticket_id)
            
            # التعامل مع أزرار نقل التذكرة
            elif custom_id.startswith('move_'):
                ticket_id = custom_id.replace('move_', '')
                await handle_move_ticket(interaction, ticket_id)
            
            # التعامل مع أزرار إشعار العميل
            elif custom_id.startswith('notify_'):
                ticket_id = custom_id.replace('notify_', '')
                await handle_notify_user(interaction, ticket_id)
            
            # التعامل مع أزرار حل التذكرة
            elif custom_id.startswith('solve_'):
                ticket_id = custom_id.replace('solve_', '')
                await handle_solve_ticket(interaction, ticket_id)
            
            # التعامل مع أزرار إضافة عضو
            elif custom_id.startswith('add_'):
                ticket_id = custom_id.replace('add_', '')
                await handle_add_member(interaction, ticket_id)
            
            # التعامل مع أزرار تغيير الاسم
            elif custom_id.startswith('rename_'):
                ticket_id = custom_id.replace('rename_', '')
                await handle_rename_ticket(interaction, ticket_id)
                
    except Exception as e:
        print(f"Error handling interaction: {e}")

async def handle_close_ticket(interaction: discord.Interaction, ticket_id):
    # البحث عن التذكرة في قاعدة البيانات
    async with bot.db.pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(
                "SELECT user_id, status FROM tickets WHERE ticket_id = %s",
                (ticket_id,)
            )
            result = await cur.fetchone()
            
            if not result:
                await interaction.response.send_message("لم يتم العثور على التذكرة", ephemeral=True)
                return
                
            user_id, status = result
            
            # تحديث حالة التذكرة
            await cur.execute(
                "UPDATE tickets SET status = 'closed', closed_at = NOW(), closed_by = %s WHERE ticket_id = %s",
                (interaction.user.id, ticket_id)
            )
            
            # تحديث إحصائيات الأدمن
            await cur.execute(
                "INSERT INTO admin_stats (admin_id, tickets_closed) VALUES (%s, 1) ON DUPLICATE KEY UPDATE tickets_closed = tickets_closed + 1",
                (interaction.user.id,)
            )
    
    # تغيير اسم الروم
    await interaction.channel.edit(name=interaction.channel.name.replace("⚪", "🔒").replace("🟢", "🔒").replace("✅", "🔒"))
    
    # نقل الروم إلى قسم التذاكر المغلقة
    closed_category_id = Config.CATEGORY_IDS.get('closed_tickets')
    if closed_category_id:
        closed_category = interaction.guild.get_channel(closed_category_id)
    else:
        closed_category = await interaction.guild.create_category("التذاكر المغلقة")
    
    await interaction.channel.edit(category=closed_category)
    
    # إرسال رسالة الإغلاق
    embed = discord.Embed(
        title="تم إغلاق التذكرة",
        description=f"تم إغلاق هذه التذكرة بواسطة {interaction.user.mention}",
        color=Config.COLORS['closed']
    )
    await interaction.response.send_message(embed=embed)
    
    # إزالة صلاحيات الكتابة من المستخدم
    member = interaction.guild.get_member(user_id)
    if member:
        await interaction.channel.set_permissions(member, send_messages=False, read_messages=True)

# سيتم تنفيذ الدوال الأخرى (handle_move_ticket, handle_notify_user, etc.) بنفس النمط

if __name__ == "__main__":
    bot.run(Config.TOKEN)