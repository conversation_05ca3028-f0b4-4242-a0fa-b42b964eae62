import aiomysql
import discord
from discord import SelectOption, ButtonStyle
from discord.ui import Select, Button, View
from config import Config
import asyncio

class TicketView(View):
    def __init__(self, bot):
        super().__init__(timeout=None)
        self.bot = bot
        self.add_item(TicketCategorySelect())
    
    async def on_timeout(self):
        # لا تفعل شيئاً عند انتهاء الوقت للحفاظ على الاستمرارية
        pass

class TicketCategorySelect(Select):
    def __init__(self):
        options = [
            SelectOption(label="استفسارات", value="inquiries", emoji="❓", description="للاستفسارات العامة"),
            SelectOption(label="مشاكل", value="problems", emoji="⚠️", description="للمشاكل التقنية"),
            SelectOption(label="فواتير", value="billing", emoji="💳", description="للفواتير والمدفوعات")
        ]
        super().__init__(
            placeholder="اختر قسم الدعم الفني",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="ticket_category_select"
        )
    
    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        
        # التحقق من وجود تذكرة مفتوحة
        async with self.view.bot.db.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cur:
                await cur.execute(
                    "SELECT * FROM tickets WHERE user_id = %s AND status IN ('open', 'pending')",
                    (interaction.user.id,)
                )
                existing_ticket = await cur.fetchone()
                
                if existing_ticket:
                    embed = discord.Embed(
                        title="خطأ",
                        description="لديك تذكرة مفتوحة بالفعل. لا يمكنك فتح أكثر من تذكرة في نفس الوقت.",
                        color=Config.COLORS['closed']
                    )
                    await interaction.followup.send(embed=embed, ephemeral=True)
                    return
        
        # فتح تذكرة جديدة
        category = self.values[0]
        ticket_number = await self.view.bot.get_ticket_number()
        ticket_id = f"ticket-{ticket_number:04d}"
        
        # الحصول على إعدادات القسم
        category_config = Config.CATEGORY_MESSAGES.get(category, {})
        role_id = Config.ROLE_IDS.get(category)
        
        # إنشاء روم جديد للتذكرة
        overwrites = {
            interaction.guild.default_role: discord.PermissionOverwrite(view_channel=False),
            interaction.user: discord.PermissionOverwrite(view_channel=True, send_messages=True, read_message_history=True),
            interaction.guild.me: discord.PermissionOverwrite(view_channel=True, send_messages=True, manage_messages=True, manage_channels=True)
        }
        
        # إضافة صلاحيات الأدمن بناء على القسم
        if role_id:
            role = interaction.guild.get_role(role_id)
            if role:
                overwrites[role] = discord.PermissionOverwrite(view_channel=True, send_messages=True, read_message_history=True)
        
        # الحصول على قسم التذاكر المفتوحة
        open_category_id = Config.CATEGORY_IDS.get('open_tickets')
        if open_category_id:
            category_channel = interaction.guild.get_channel(open_category_id)
        else:
            category_channel = None
            
        if not category_channel:
            category_channel = await interaction.guild.create_category("التذاكر المفتوحة")
        
        # إنشاء قناة التذكرة
        channel = await interaction.guild.create_text_channel(
            name=f"⚪-{ticket_id}",
            category=category_channel,
            overwrites=overwrites
        )
        
        # حفظ التذكرة في قاعدة البيانات
        async with self.view.bot.db.pool.acquire() as conn:
            async with conn.cursor() as cur:
                await cur.execute(
                    "INSERT INTO tickets (ticket_id, user_id, channel_id, category, title) VALUES (%s, %s, %s, %s, %s)",
                    (ticket_id, interaction.user.id, channel.id, category, f"تذكرة {category}")
                )
        
        # إرسال رسالة الترحيب
        embed = discord.Embed(
            title=category_config.get('title', f'قسم {category}'),
            description=category_config.get('description', 'شكراً لتواصلك مع دعمنا الفني.'),
            color=category_config.get('color', Config.COLORS['open'])
        )
        embed.add_field(name="رقم التذكرة", value=ticket_id, inline=True)
        embed.add_field(name="القسم", value=category, inline=True)
        embed.set_footer(text=f"طلب من قبل: {interaction.user}", icon_url=interaction.user.avatar.url)
        
        # إنشاء واجهة الأزرار
        view = TicketActionsView(self.view.bot, ticket_id, interaction.user.id)
        
        # إرسال الرسالة مع منشن للأدمن
        mention = f"<@&{role_id}>" if role_id else ""
        await channel.send(content=f"{interaction.user.mention} {mention}", embed=embed, view=view)
        
        # إرسال تأكيد للعميل
        embed = discord.Embed(
            title="تم فتح التذكرة بنجاح",
            description=f"تم إنشاء تذكرتك في {channel.mention}",
            color=Config.COLORS['open']
        )
        await interaction.followup.send(embed=embed, ephemeral=True)

class TicketActionsView(View):
    def __init__(self, bot, ticket_id, user_id):
        super().__init__(timeout=None)
        self.bot = bot
        self.ticket_id = ticket_id
        self.user_id = user_id
        
        # أزرار التحكم بالتذكرة
        self.add_item(Button(style=ButtonStyle.red, label="إغلاق التذكرة", emoji="🔒", custom_id=f"close_{ticket_id}"))
        self.add_item(Button(style=ButtonStyle.blurple, label="نقل التذكرة", emoji="↪️", custom_id=f"move_{ticket_id}"))
        self.add_item(Button(style=ButtonStyle.green, label="إشعار العميل", emoji="📨", custom_id=f"notify_{ticket_id}"))
        self.add_item(Button(style=ButtonStyle.green, label="تم الحل", emoji="✅", custom_id=f"solve_{ticket_id}"))
        self.add_item(Button(style=ButtonStyle.gray, label="إضافة عضو", emoji="👥", custom_id=f"add_{ticket_id}"))
        self.add_item(Button(style=ButtonStyle.gray, label="تغيير الاسم", emoji="📝", custom_id=f"rename_{ticket_id}"))
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # التحقق من صلاحية المستخدم
        is_admin = any(role.id in Config.ROLE_IDS.values() for role in interaction.user.roles)
        is_owner = interaction.user.id == self.user_id
        
        # بعض الأزرار متاحة للعميل فقط
        button_id = interaction.data['custom_id']
        if button_id == f"close_{self.ticket_id}":
            return is_admin or is_owner
        elif button_id in [f"move_{self.ticket_id}", f"notify_{self.ticket_id}", 
                          f"solve_{self.ticket_id}", f"add_{self.ticket_id}", 
                          f"rename_{self.ticket_id}"]:
            return is_admin
        
        return False
    
    async def on_timeout(self):
        # لا تفعل شيئاً عند انتهاء الوقت للحفاظ على الاستمرارية
        pass

async def setup_ticket_system(bot):
    # إضافة الـ Views المستمرة
    bot.add_view(TicketView(bot))