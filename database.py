import pymysql
import aiomysql
from config import Config

class Database:
    def __init__(self):
        self.pool = None

    async def create_pool(self):
        self.pool = await aiomysql.create_pool(
            host=Config.DB_HOST,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD,
            db=Config.DB_NAME,
            autocommit=True,
            minsize=1,
            maxsize=10
        )
        await self.create_tables()

    async def create_tables(self):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cur:
                # جدول التذاكر
                await cur.execute('''
                CREATE TABLE IF NOT EXISTS tickets (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ticket_id VARCHAR(20) UNIQUE,
                    user_id BIGINT NOT NULL,
                    channel_id BIGINT NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    status ENUM('open', 'pending', 'closed', 'solved') DEFAULT 'open',
                    title TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    closed_at TIMESTAMP NULL,
                    solved_at TIMESTAMP NULL,
                    closed_by BIGINT NULL,
                    solved_by BIGINT NULL
                )
                ''')

                # جدول الردود
                await cur.execute('''
                CREATE TABLE IF NOT EXISTS ticket_messages (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ticket_id VARCHAR(20) NOT NULL,
                    user_id BIGINT NOT NULL,
                    message TEXT,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ticket_id) REFERENCES tickets(ticket_id) ON DELETE CASCADE
                )
                ''')

                # جدول الإحصائيات
                await cur.execute('''
                CREATE TABLE IF NOT EXISTS admin_stats (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    admin_id BIGINT NOT NULL,
                    tickets_closed INT DEFAULT 0,
                    tickets_solved INT DEFAULT 0,
                    avg_response_time FLOAT DEFAULT 0,
                    total_responses INT DEFAULT 0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
                ''')

                # جدول الأعضاء المضافين للتذاكر
                await cur.execute('''
                CREATE TABLE IF NOT EXISTS ticket_members (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ticket_id VARCHAR(20) NOT NULL,
                    user_id BIGINT NOT NULL,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ticket_id) REFERENCES tickets(ticket_id) ON DELETE CASCADE
                )
                ''')

db = Database()