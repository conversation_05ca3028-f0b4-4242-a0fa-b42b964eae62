import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    TOKEN = os.getenv('DISCORD_BOT_TOKEN')
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_USER = os.getenv('DB_USER', 'root')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    DB_NAME = os.getenv('DB_NAME', 'discord_tickets')
    
    # إعدادات الأدوار لكل قسم
    ROLE_IDS = {
        'inquiries': int(os.getenv('INQUIRIES_ROLE_ID', 0)),  # اداري الاستفسارات
        'problems': int(os.getenv('PROBLEMS_ROLE_ID', 0)),    # اداري المشاكل
        'billing': int(os.getenv('BILLING_ROLE_ID', 0)),      # اداري الفواتير
        'tickets_admin': int(os.getenv('TICKETS_ADMIN_ROLE_ID', 0))  # اداري التذاكر العام
    }
    
    # إعدادات الأقسام (الكاتيجوريات)
    CATEGORY_IDS = {
        'open_tickets': int(os.getenv('OPEN_TICKETS_CATEGORY_ID', 0)),      # التذاكر المفتوحة
        'closed_tickets': int(os.getenv('CLOSED_TICKETS_CATEGORY_ID', 0)),   # التذاكر المغلقة
        'inquiries': int(os.getenv('INQUIRIES_CATEGORY_ID', 0)),            # قسم الاستفسارات
        'problems': int(os.getenv('PROBLEMS_CATEGORY_ID', 0)),              # قسم المشاكل
        'billing': int(os.getenv('BILLING_CATEGORY_ID', 0))                 # قسم الفواتير
    }
    
    # الرسائل الخاصة لكل قسم
    CATEGORY_MESSAGES = {
        'inquiries': {
            'title': 'قسم الاستفسارات',
            'description': 'مرحباً بك في قسم الاستفسارات! فريق الدعم سيساعدك في الإجابة على جميع استفساراتك.',
            'color': 0x3498db  # أزرق
        },
        'problems': {
            'title': 'قسم المشاكل التقنية',
            'description': 'مرحباً بك في قسم المشاكل التقنية! فريق الدعم سيساعدك في حل أي مشكلة تواجهك.',
            'color': 0xe74c3c  # أحمر
        },
        'billing': {
            'title': 'قسم الفواتير والمدفوعات',
            'description': 'مرحباً بك في قسم الفواتير والمدفوعات! فريق الدعم سيساعدك في جميع استفساراتك المالية.',
            'color': 0x2ecc71  # أخضر
        }
    }
    
    # ألوان Embed عامة
    COLORS = {
        'open': 0x00ff00,      # أخضر
        'pending': 0xffff00,   # أصفر
        'closed': 0xff0000,    # أحمر
        'solved': 0x0080ff,    # أزرق
        'info': 0x800080       # بنفسجي
    }